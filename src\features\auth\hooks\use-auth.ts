import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import { authApi } from "../api";
import { tokenUtils } from "@/utils/token-utils";
import type { LoginRequest, User } from "../api/types";

// Query keys for React Query
export const authKeys = {
  all: ["auth"] as const,
  profile: () => [...authKeys.all, "profile"] as const,
};

// Hook for login mutation
export const useLogin = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (credentials: LoginRequest) => authApi.login(credentials),
    onSuccess: (data) => {
      // Store the access token using token utilities
      tokenUtils.setToken(data.token);

      // Store user data in localStorage for persistence across reloads
      tokenUtils.setUserData(data.user);

      // Set user data in cache
      queryClient.setQueryData(authKeys.profile(), data.user);

      // Show success toast
      toast.success("Login successful! Welcome back.", {
        duration: 3000,
      });

      // Navigate to dashboard with a small delay to ensure token and cache are set
      setTimeout(() => {
        navigate("/dashboard", { replace: true });
      }, 200);
    },
    onError: (error: any) => {
      console.error("Login failed:", error);

      // Show error toast
      const errorMessage = error?.response?.data?.message || "Login failed. Please check your credentials.";
      toast.error(errorMessage);

      // Remove any existing token on login failure
      tokenUtils.removeToken();
    },
  });
};

// Hook for logout
export const useLogout = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => authApi.logout(),
    onSuccess: () => {
      // Clear all auth-related data
      tokenUtils.clearAuthData();

      // Clear all queries from cache
      queryClient.clear();

      // Show success toast
      toast.success("Logged out successfully!");

      // Navigate to login
      navigate("/auth", { replace: true });
    },
    onError: () => {
      // Even if logout API fails, clear local data
      tokenUtils.clearAuthData();
      queryClient.clear();

      // Show success toast anyway since user is logged out
      toast.success("Logged out successfully!");

      navigate("/auth", { replace: true });
    },
  });
};

// Hook for getting user profile
export const useProfile = () => {
  return useQuery({
    queryKey: authKeys.profile(),
    queryFn: () => authApi.getProfile(),
    enabled: false, // Disable automatic fetching, we'll rely on cached data from login
    retry: false, // Don't retry on failure
    select: (data) => data.user || data.data, // Extract user data from response (handle both formats)
  });
};

// Hook to check if user is authenticated
export const useIsAuthenticated = () => {
  const queryClient = useQueryClient();
  const hasValidToken = tokenUtils.hasValidToken();

  // If there's no valid token, user is definitely not authenticated
  if (!hasValidToken) {
    // Clear any stale cache data
    queryClient.removeQueries({ queryKey: authKeys.profile() });
    return {
      isAuthenticated: false,
      isLoading: false,
      user: undefined,
    };
  }

  // If we have a valid token, check if we have cached user data
  let cachedUser = queryClient.getQueryData(authKeys.profile());

  // If no cached user data, try to restore from localStorage
  if (!cachedUser) {
    const persistedUser = tokenUtils.getUserData();

    if (persistedUser) {
      // Restore user data to cache
      queryClient.setQueryData(authKeys.profile(), persistedUser);
      cachedUser = persistedUser;
    }
  }

  // If we have user data (cached or restored), user is authenticated
  if (cachedUser) {
    return {
      isAuthenticated: true,
      isLoading: false,
      user: cachedUser,
    };
  }

  // If we have a valid token but no user data, we're not authenticated yet
  return {
    isAuthenticated: false,
    isLoading: false,
    user: undefined,
  };
};

// Hook to get current user data
export const useCurrentUser = (): User | undefined => {
  const { data: user } = useProfile();
  return user;
};
