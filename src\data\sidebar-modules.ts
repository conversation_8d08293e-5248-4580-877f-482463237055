import {
  MdDashboard,
  MdReceipt,
  MdPeople,
  MdPayment,
  MdSettings
} from "react-icons/md";
import type { IconType } from "react-icons";

export interface SidebarModule {
  id: string;
  name: string;
  url: string;
  icon: IconType;
}

export const sidebarModules: SidebarModule[] = [
  {
    id: "dashboard",
    name: "Dashboard",
    url: "/dashboard",
    icon: MdDashboard,
  },
  {
    id: "invoice",
    name: "Invoice",
    url: "/dashboard/invoice",
    icon: MdReceipt,
  },
  {
    id: "clients",
    name: "Clients",
    url: "/dashboard/clients",
    icon: MdPeople,
  },
  {
    id: "payments",
    name: "Payments",
    url: "/dashboard/payments",
    icon: MdPayment,
  },
  {
    id: "settings",
    name: "Settings",
    url: "/dashboard/settings",
    icon: MdSettings,
  },
];
