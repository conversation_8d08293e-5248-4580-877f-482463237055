import { Route, Routes } from "react-router-dom";
import DashboardLayout from "../features/(dashbaord)/components/layout/dashboard-layout";
import DashboardRoute from "../features/(dashbaord)/dashboard";
import InvoiceRoute from "../features/(dashbaord)/invoice";
import ClientsRoute from "../features/(dashbaord)/clients";
import PaymentsRoute from "../features/(dashbaord)/payments";
import SettingsRoute from "../features/(dashbaord)/settings";

export const ProtectedRoutes = () => {
  return (
    <Routes>
      <Route element={<DashboardLayout />}>
        <Route path="/" element={<DashboardRoute />} />
        <Route path="/invoice/*" element={<InvoiceRoute />} />
        <Route path="/clients/*" element={<ClientsRoute />} />
        <Route path="/payments/*" element={<PaymentsRoute />} />
        <Route path="/settings/*" element={<SettingsRoute />} />
      </Route>
    </Routes>
  );
};
