// Token management utilities

const TOKEN_KEY = "access_token";
const USER_KEY = "user_data";

export const tokenUtils = {
  // Get token from localStorage
  getToken: (): string | null => {
    try {
      const token = localStorage.getItem(TOKEN_KEY);
      // Return null if token is invalid
      if (!token || token === "undefined" || token === "null" || token.trim() === "") {
        return null;
      }
      return token;
    } catch (error) {
      console.error("Error getting token:", error);
      return null;
    }
  },

  // Set token in localStorage
  setToken: (token: string): void => {
    try {
      if (!token || token.trim() === "") {
        console.error("Attempted to set empty token");
        return;
      }
      localStorage.setItem(TOKEN_KEY, token);
      console.log("Token set successfully");
    } catch (error) {
      console.error("Error setting token:", error);
    }
  },

  // Remove token from localStorage
  removeToken: (): void => {
    try {
      localStorage.removeItem(TOKEN_KEY);
      console.log("Token removed successfully");
    } catch (error) {
      console.error("Error removing token:", error);
    }
  },

  // Check if user has a valid token
  hasValidToken: (): boolean => {
    const token = tokenUtils.getToken();
    return token !== null;
  },

  // Get user data from localStorage
  getUserData: (): any | null => {
    try {
      const userData = localStorage.getItem(USER_KEY);
      if (!userData || userData === "undefined" || userData === "null") {
        return null;
      }
      return JSON.parse(userData);
    } catch (error) {
      console.error("Error getting user data:", error);
      return null;
    }
  },

  // Set user data in localStorage
  setUserData: (userData: any): void => {
    try {
      if (!userData) {
        console.error("Attempted to set empty user data");
        return;
      }
      localStorage.setItem(USER_KEY, JSON.stringify(userData));
      console.log("User data set successfully");
    } catch (error) {
      console.error("Error setting user data:", error);
    }
  },

  // Clear all auth-related data
  clearAuthData: (): void => {
    try {
      localStorage.removeItem(TOKEN_KEY);
      localStorage.removeItem(USER_KEY);
      console.log("All auth data cleared");
    } catch (error) {
      console.error("Error clearing auth data:", error);
    }
  }
};
