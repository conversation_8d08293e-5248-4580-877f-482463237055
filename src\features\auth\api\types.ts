// Authentication API types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  message: string;
  token: string;
  user: User;
}

export interface User {
  id: string;
  email: string;
  name: string;
  role?: string;
  // Add other user properties as needed
}

export interface AuthError {
  message: string;
  status?: number;
}

export interface ProfileResponse {
  message?: string;
  user?: User;
  data?: User; // Keep both for flexibility
}
