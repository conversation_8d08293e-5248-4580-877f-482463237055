import { customAxios } from "@/utils/axios-interceptors";
import type { LoginRequest, LoginResponse, ProfileResponse } from "./types";

export const authApi = {
  // Login API
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response = await customAxios.post<LoginResponse>("/auth/login", credentials);
    return response.data;
  },

  // Get user profile
  getProfile: async (): Promise<ProfileResponse> => {
    const response = await customAxios.get<ProfileResponse>("/auth/profile");
    return response.data;
  },

  // Logout (if needed for server-side logout)
  logout: async (): Promise<void> => {
    try {
      await customAxios.post("/auth/logout");
    } catch (error) {
      // Handle logout error if needed
      console.warn("Logout API call failed:", error);
    }
  },
};
