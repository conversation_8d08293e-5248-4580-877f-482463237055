# Authentication Setup with TanStack Query

## Overview
This project has been successfully integrated with TanStack Query for authentication management, replacing the previous Redux implementation. The authentication flow includes login, logout, and protected routes.

## API Integration
- **Base URL**: `{{base_url}}/auth/login`
- **Login Endpoint**: `POST /auth/login`
- **Profile Endpoint**: `GET /auth/profile`
- **Request Format**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "1234567890"
  }
  ```

## Features Implemented

### 1. Authentication API Layer
- **Location**: `src/features/auth/api/`
- **Files**:
  - `auth-api.ts` - API functions for login, profile, logout
  - `types.ts` - TypeScript interfaces for API requests/responses
  - `index.ts` - Export barrel file

### 2. TanStack Query Hooks
- **Location**: `src/features/auth/hooks/`
- **Hooks**:
  - `useLogin()` - Login mutation with automatic token storage and navigation
  - `useLogout()` - Logout mutation with token cleanup and navigation
  - `useProfile()` - User profile query
  - `useIsAuthenticated()` - Authentication status checker
  - `useCurrentUser()` - Current user data getter

### 3. Updated Components

#### Login Component (`src/features/auth/routes/login.tsx`)
- Integrated with `useLogin()` hook
- Real API call to login endpoint
- Error handling and display
- Loading states during authentication

#### Private Route (`src/routes/private-route.tsx`)
- Replaced Redux with TanStack Query
- Uses `useIsAuthenticated()` for auth checks
- Automatic redirect to login if not authenticated
- Loading state while checking authentication

#### Top Navbar (`src/features/(dashbaord)/components/layout/top-navbar.tsx`)
- Displays current user information
- Dropdown menu with user details
- Logout functionality with `useLogout()` hook
- Click outside to close dropdown

### 4. Router Configuration
- Fixed token key from `"token"` to `"access_token"`
- Proper navigation flow: login → dashboard → logout → login
- Protected routes with authentication checks

## Authentication Flow

### 1. Application Start
- App checks for `access_token` in localStorage
- If token exists: redirects to `/dashboard`
- If no token: redirects to `/auth` (login page)

### 2. Login Process
1. User enters email and password
2. Form validation using Zod schema
3. API call to `/auth/login` endpoint
4. On success:
   - Token stored in localStorage as `access_token`
   - User data cached in TanStack Query
   - Automatic redirect to `/dashboard`
5. On error:
   - Error message displayed to user
   - Token removed from localStorage

### 3. Protected Routes
1. `PrivateRoute` component checks authentication
2. If authenticated: renders dashboard content
3. If not authenticated: redirects to login
4. Loading state shown during auth check

### 4. Logout Process
1. User clicks logout in dropdown menu
2. API call to `/auth/logout` (optional)
3. Token removed from localStorage
4. TanStack Query cache cleared
5. Automatic redirect to `/auth`

## Configuration

### Environment Variables
```env
VITE_API_BASE_URL=http://localhost:3000/v1/
```

### Path Aliases
- Configured `@/` alias pointing to `src/`
- Updated both `vite.config.ts` and `tsconfig.app.json`

## Testing Instructions

### 1. Start Development Server
```bash
npm run dev
```

### 2. Test Authentication Flow
1. **Initial Load**: Should redirect to login page
2. **Login**: Use the provided credentials:
   - Email: `<EMAIL>`
   - Password: `1234567890`
3. **Dashboard**: Should redirect to dashboard after successful login
4. **User Display**: Top navbar should show user information
5. **Logout**: Click user dropdown and select "Sign out"
6. **Redirect**: Should redirect back to login page

### 3. Test Error Handling
- Try invalid credentials to see error messages
- Check network tab for API calls
- Verify token storage in localStorage

## File Structure
```
src/
├── features/
│   └── auth/
│       ├── api/
│       │   ├── auth-api.ts
│       │   ├── types.ts
│       │   └── index.ts
│       ├── hooks/
│       │   ├── use-auth.ts
│       │   └── index.ts
│       └── routes/
│           └── login.tsx (updated)
├── routes/
│   ├── private-route.tsx (updated)
│   └── router.tsx (updated)
└── features/(dashboard)/
    └── components/layout/
        └── top-navbar.tsx (updated)
```

## Next Steps
- Test with real backend API
- Add refresh token functionality if needed
- Implement remember me functionality
- Add loading spinners for better UX
- Consider adding toast notifications for better feedback
