import { Navigate, Outlet, useNavigate } from "react-router-dom";
import { useIsAuthenticated } from "@/features/auth/hooks";
import { tokenUtils } from "@/utils/token-utils";
import { useEffect } from "react";

const PrivateRoute = () => {
  const navigate = useNavigate();
  const { isAuthenticated, isLoading } = useIsAuthenticated();
  const hasValidToken = tokenUtils.hasValidToken();

  useEffect(() => {
    // If no valid token, redirect to auth immediately
    if (!hasValidToken) {
      navigate("/auth");
      return;
    }
  }, [hasValidToken, navigate]);

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  // If not authenticated (token invalid or user fetch failed), redirect to auth
  if (!isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  // If authenticated, render the protected routes
  return <Outlet />;
};

export default PrivateRoute;
