import {
  createBrowserRouter,
  createRoutesFromElements,
  Route,
  Navigate,
} from "react-router-dom";
import { AuthRoutes } from "../features/auth/routes";
import { ProtectedRoutes } from "./protected-routes";
import NotFoundPage from "../global_url/not-found";
import PrivateRoute from "./private-route";
import { tokenUtils } from "@/utils/token-utils";

export const router = createBrowserRouter(
  createRoutesFromElements(
    <Route>
      <Route
        path="/"
        element={
          tokenUtils.hasValidToken() ? (
            <Navigate to="/dashboard" replace />
          ) : (
            <Navigate to="/auth" replace />
          )
        }
      />
      <Route path="/auth" element={<AuthRoutes />} />
      <Route element={<PrivateRoute />}>
        <Route path="/dashboard/*" element={<ProtectedRoutes />} />
      </Route>
      <Route path="*" element={<NotFoundPage />} />
    </Route>
  )
);
